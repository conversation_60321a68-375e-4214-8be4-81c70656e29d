import os
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
from torchvision import models
import torch
import torch.nn as nn
import torch.optim as optim

from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from torchvision.utils import make_grid
from PIL import Image
import torch.nn.functional as F
import torchvision
import random
import csv
from torch.optim.lr_scheduler import StepLR

torch.manual_seed(42)
np.random.seed(42)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

output_dir = "working/output/image"
output_test = "working/output/test"
output_modul = "working/output/modul"
output_loss = "working/output/loss"
os.makedirs(output_dir, exist_ok=True)
os.makedirs(output_test, exist_ok=True)
os.makedirs(output_modul, exist_ok=True)
os.makedirs(output_loss, exist_ok=True)

class ImageDataset(Dataset):
    def __init__(self, root_dir, transform=None):
        self.root_dir = root_dir
        self.transform = transform
        self.images = [f for f in os.listdir(root_dir) if f.endswith('.jpg') or f.endswith('.png')]
        print(f"Found {len(self.images)} images in {root_dir}")
        self.processed_images = []
        for img_name in tqdm(self.images, desc="Preprocessing images"):
            img_path = os.path.join(self.root_dir, img_name)
            image = Image.open(img_path).convert('RGB')
            if self.transform:
                image = self.transform(image)
            self.processed_images.append(image)
        print("Preprocessing completed")

    def __len__(self):
        return len(self.processed_images)

    def __getitem__(self, idx):
        image = self.processed_images[idx]
        noise_amplitude = 0.5
        noise = torch.randn_like(image) * noise_amplitude
        corrupted = image + noise
        return corrupted, image

class AttentionBlock(nn.Module):
    def __init__(self, in_channels):
        super(AttentionBlock, self).__init__()
        self.conv = nn.Conv2d(in_channels, 1, kernel_size=1)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        attention = self.sigmoid(self.conv(x))
        return x * attention

class Generator(nn.Module):
    def __init__(self):
        super(Generator, self).__init__()
        self.encoder = nn.Sequential(
            nn.Conv2d(3, 64, kernel_size=7, stride=1, padding=3),
            nn.InstanceNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 128, kernel_size=3, stride=2, padding=1),
            nn.InstanceNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 256, kernel_size=3, stride=2, padding=1),
            nn.InstanceNorm2d(256),
            nn.ReLU(inplace=True),
        )
        
        self.residual_blocks = nn.Sequential(
            *[ResidualBlock(256) for _ in range(6)]
        )
        
        self.decoder = nn.Sequential(
            nn.ConvTranspose2d(256, 128, kernel_size=3, stride=2, padding=1, output_padding=1),
            nn.InstanceNorm2d(128),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(128, 64, kernel_size=3, stride=2, padding=1, output_padding=1),
            nn.InstanceNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 3, kernel_size=7, stride=1, padding=3),
            nn.Tanh()
        )
        
        self.attention = AttentionBlock(256)

    def forward(self, x):
        x = self.encoder(x)
        x = self.residual_blocks(x)
        x = self.attention(x)
        x = self.decoder(x)
        return x

class ResidualBlock(nn.Module):
    def __init__(self, channels):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv2d(channels, channels, kernel_size=3, stride=1, padding=1)
        self.in1 = nn.InstanceNorm2d(channels)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(channels, channels, kernel_size=3, stride=1, padding=1)
        self.in2 = nn.InstanceNorm2d(channels)

    def forward(self, x):
        residual = x
        out = self.relu(self.in1(self.conv1(x)))
        out = self.in2(self.conv2(out))
        out += residual
        out = self.relu(out)
        return out
    
class Discriminator(nn.Module):
    def __init__(self):
        super(Discriminator, self).__init__()
        self.model = nn.Sequential(
            nn.Conv2d(3, 64, kernel_size=4, stride=2, padding=1),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(64, 128, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm2d(128),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(128, 256, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm2d(256),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(256, 512, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm2d(512),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(512, 1, kernel_size=4, stride=1, padding=1),
            nn.Sigmoid()
        )

    def forward(self, x):
        return self.model(x)
    
batch_size = 8
num_epochs = 30
lr_d = 0.0001
lr_g = 0.0001
beta1 = 0.5

transform = transforms.Compose([
    transforms.Resize((256, 256)),
    transforms.ToTensor(),
    transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
])

from PIL import ImageFile
ImageFile.LOAD_TRUNCATED_IMAGES = True

print("Loading training data...")
train_dataset = ImageDataset(root_dir="Humans", transform=transform)
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)

generator = Generator().to(device)
discriminator = Discriminator().to(device)

d_optimizer = optim.Adam(discriminator.parameters(), lr=lr_d, betas=(beta1, 0.999))
g_optimizer = optim.Adam(generator.parameters(), lr=lr_g, betas=(beta1, 0.999))

# 添加学习率调度器
g_scheduler = StepLR(g_optimizer, step_size=50, gamma=0.5)
d_scheduler = StepLR(d_optimizer, step_size=50, gamma=0.5)

def perceptual_loss(vgg, fake, real):
    fake_features = vgg(fake)
    real_features = vgg(real)
    return F.l1_loss(fake_features, real_features)

vgg = models.vgg16()
vgg.load_state_dict(torch.load('vgg16-397923af.pth'))
vgg = vgg.features.to(device).eval()

for param in vgg.parameters():
    param.requires_grad = False
    
lambda_l1 = 10
lambda_p = 20

g_losses = []
d_losses = []
g_lr_history = []
d_lr_history = []

for epoch in range(num_epochs):
    generator.train()
    discriminator.train()
    
    total_d_loss = 0
    total_g_loss = 0
    
    for i, (corrupted, real) in enumerate(tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}")):
        corrupted = corrupted.to(device)
        real = real.to(device)
        batch_size = real.size(0)
        
        # 训练生成器
        g_optimizer.zero_grad()
        
        fake = generator(corrupted)
        fake_output = discriminator(fake)
        
        g_loss_adv = F.mse_loss(fake_output, torch.ones_like(fake_output))
        l1 = F.l1_loss(fake, real)
        p_loss = perceptual_loss(vgg, fake, real)
        
        total_weight = 1 + lambda_l1 + lambda_p
        g_loss = (g_loss_adv + lambda_l1 * l1 + lambda_p * p_loss) / total_weight
        
        g_loss.backward()
        g_optimizer.step()
        
        total_g_loss += g_loss.item()
        
        # 训练判别器（每 n 次迭代训练一次）
        if i % 25 == 0:  # 多次迭代训练一次判别器
            d_optimizer.zero_grad()
            
            # 真实图像的判别结果
            real_output = discriminator(real)
            d_loss_real = F.mse_loss(real_output, torch.ones_like(real_output))
            
            # 生成假图像
            with torch.no_grad():
                fake = generator(corrupted)
            
            # 假图像的判别结果
            fake_output = discriminator(fake.detach())
            d_loss_fake = F.mse_loss(fake_output, torch.zeros_like(fake_output))
            
            # 判别器总损失
            d_loss = d_loss_real - d_loss_fake
            
            d_loss.backward()
            d_optimizer.step()
            
            total_d_loss += d_loss.item()
        
    avg_g_loss = total_g_loss / len(train_loader)
    avg_d_loss = total_d_loss / (len(train_loader) // 25)  # 因为判别器多次迭代训练一次
    
    g_losses.append(avg_g_loss)
    d_losses.append(avg_d_loss)
    
    # 更新学习率
    g_scheduler.step()
    d_scheduler.step()
    
    # 记录学习率
    g_lr_history.append(g_scheduler.get_last_lr()[0])
    d_lr_history.append(d_scheduler.get_last_lr()[0])
    
    print(f"Epoch [{epoch+1}/{num_epochs}], d_loss: {avg_d_loss:.4f}, g_loss: {avg_g_loss:.4f}")
    print(f"Current learning rate: G: {g_scheduler.get_last_lr()[0]:.6f}, D: {d_scheduler.get_last_lr()[0]:.6f}")
    
    if (epoch + 1) % 50 == 0:
        generator.eval()
        with torch.no_grad():
            fake = generator(corrupted[:4])
        img_grid = make_grid(fake, normalize=True, nrow=2)
        plt.figure(figsize=(20, 20))
        plt.imshow(img_grid.permute(1, 2, 0).cpu())
        plt.axis('off')
        plt.savefig(os.path.join(output_dir, f'generated_images_epoch_{epoch+1}.png'), dpi=300, bbox_inches='tight')
        plt.close()

        torch.save(generator.state_dict(), os.path.join(output_modul, f'generator_epoch_{epoch+1}.pth'))
        torch.save(discriminator.state_dict(), os.path.join(output_modul, f'discriminator_epoch_{epoch+1}.pth'))

torch.save(generator.state_dict(), os.path.join(output_modul, 'generator_final.pth'))
torch.save(discriminator.state_dict(), os.path.join(output_modul, 'discriminator_final.pth'))

# 保存loss数据
with open(os.path.join(output_loss, 'generator_loss.csv'), 'w', newline='') as f:
    writer = csv.writer(f)
    writer.writerow(['Epoch', 'Loss'])
    for epoch, loss in enumerate(g_losses, 1):
        writer.writerow([epoch, loss])

with open(os.path.join(output_loss, 'discriminator_loss.csv'), 'w', newline='') as f:
    writer = csv.writer(f)
    writer.writerow(['Epoch', 'Loss'])
    for epoch, loss in enumerate(d_losses, 1):
        writer.writerow([epoch, loss])

# 绘制loss曲线
def plot_loss(g_losses, d_losses):
    plt.figure(figsize=(10, 5))
    plt.plot(range(1, len(g_losses)+1), g_losses, label='Generator')
    plt.plot(range(1, len(d_losses)+1), d_losses, label='Discriminator')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Training Loss')
    plt.savefig(os.path.join(output_loss, 'loss_plot.png'))
    plt.close()

plot_loss(g_losses, d_losses)

# 绘制学习率变化曲线
plt.figure(figsize=(10, 5))
plt.plot(range(1, num_epochs+1), g_lr_history, label='Generator')
plt.plot(range(1, num_epochs+1), d_lr_history, label='Discriminator')
plt.xlabel('Epoch')
plt.ylabel('Learning Rate')
plt.legend()
plt.title('Learning Rate Schedule')
plt.savefig(os.path.join(output_loss, 'lr_schedule.png'))
plt.close()
        
generator.eval()
test_images = []
batch_count = 0
batch_size = 10

# 测试部分
print("Loading test data...")
test_dataset = ImageDataset(root_dir="Humans", transform=transform)
test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False)

generator.eval()
with torch.no_grad():
    for i, (corrupted, real) in enumerate(tqdm(test_loader, desc="Generating test images")):
        corrupted = corrupted.to(device)
        fake = generator(corrupted)
        
        # 反归一化
        fake = fake * 0.5 + 0.5
        corrupted = corrupted * 0.5 + 0.5
        real = real * 0.5 + 0.5
        
        # 拼接原图、噪声图和生成图
        img_grid = make_grid([corrupted[0], fake[0], real[0]], nrow=3)
        img_grid = img_grid.permute(1, 2, 0).cpu().numpy()
        
        plt.figure(figsize=(15, 5))
        plt.imshow(img_grid)
        plt.axis('off')
        plt.title(f"Corrupted - Generated - Original")
        plt.savefig(os.path.join(output_test, f'test_comparison_{i+1}.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        if i >= 9:  # 只生成10张测试图片
            break

print("Testing completed.")

